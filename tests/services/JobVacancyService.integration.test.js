const JobVacancyService = require('../../src/services/JobVacancyService');
const SetJobDescriptionJob = require('../../src/jobs/SetJobDescriptionJob');
const SetVacancyGroupVariablesJob = require('../../src/jobs/SetVacancyGroupVariablesJob');
const { JobVacancy, JobTitle } = require('../../src/models');

// Mock dependencies
jest.mock('../../src/jobs/SetJobDescriptionJob');
jest.mock('../../src/jobs/SetVacancyGroupVariablesJob');
jest.mock('../../src/models');
jest.mock('../../src/repositories/JobVacanciesRepository');
jest.mock('../../src/services/OnetService');
jest.mock('../../src/services/job_vacancy/GenerateJobDescService');
jest.mock('../../src/services/external/GoogleAiService');
jest.mock('@qdrant/js-client-rest');

describe('JobVacancyService - Job Integration', () => {
  let service;
  let mockVacancy;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock vacancy
    mockVacancy = {
      id: 1,
      name: 'Software Engineer',
      status: 'draft',
      update: jest.fn().mockResolvedValue(true),
    };

    // Mock JobVacancy.create
    JobVacancy.create = jest.fn().mockResolvedValue(mockVacancy);

    // Mock JobTitle.findByPk
    JobTitle.findByPk = jest.fn().mockResolvedValue({
      name: 'Software Engineer',
    });

    // Mock job perform_async methods
    SetJobDescriptionJob.perform_async = jest.fn().mockResolvedValue({ id: 'job-123' });
    SetVacancyGroupVariablesJob.perform_async = jest.fn().mockResolvedValue({ id: 'job-456' });

    service = new JobVacancyService();

    // Mock repository
    service.repository = {
      findOne: jest.fn().mockResolvedValue(mockVacancy),
    };
  });

  describe('create', () => {
    it('should enqueue SetJobDescriptionJob after creating vacancy', async () => {
      const data = {
        name: 'Software Engineer',
        job_title_id: 1,
      };

      const result = await service.create(data);

      expect(JobVacancy.create).toHaveBeenCalledWith({
        name: 'Software Engineer',
        job_title_id: 1,
        status: 'generating_jobdesc',
      });

      expect(SetJobDescriptionJob.perform_async).toHaveBeenCalledWith({
        vacancy_id: 1,
      });

      expect(result).toBe(mockVacancy);
    });

    it('should use job title name when name is not provided', async () => {
      const data = {
        job_title_id: 1,
      };

      await service.create(data);

      expect(JobTitle.findByPk).toHaveBeenCalledWith(1);
      expect(JobVacancy.create).toHaveBeenCalledWith({
        name: 'Software Engineer',
        job_title_id: 1,
        status: 'generating_jobdesc',
      });

      expect(SetJobDescriptionJob.perform_async).toHaveBeenCalledWith({
        vacancy_id: 1,
      });
    });

    it('should handle job enqueueing errors gracefully', async () => {
      SetJobDescriptionJob.perform_async.mockRejectedValue(new Error('Queue error'));

      const data = {
        name: 'Software Engineer',
      };

      // Should still create the vacancy even if job enqueueing fails
      await expect(service.create(data)).rejects.toThrow('Queue error');

      expect(JobVacancy.create).toHaveBeenCalled();
    });
  });

  describe('update', () => {
    beforeEach(() => {
      mockVacancy.status = 'draft';
    });

    it('should enqueue SetJobDescriptionJob for generate_jobdesc followup action', async () => {
      const data = {
        name: 'Updated Software Engineer',
        followup_action: 'generate_jobdesc',
      };

      const result = await service.update(1, data);

      expect(mockVacancy.update).toHaveBeenCalledWith({
        name: 'Updated Software Engineer',
        status: 'generating_jobdesc',
      });

      expect(SetJobDescriptionJob.perform_async).toHaveBeenCalledWith({
        vacancy_id: 1,
      });

      expect(result).toBe(mockVacancy);
    });

    it('should enqueue SetVacancyGroupVariablesJob for generate_job_variables followup action', async () => {
      const data = {
        name: 'Updated Software Engineer',
        followup_action: 'generate_job_variables',
      };

      await service.update(1, data);

      expect(mockVacancy.update).toHaveBeenCalledWith({
        name: 'Updated Software Engineer',
        status: 'generating_job_variables',
      });

      expect(SetVacancyGroupVariablesJob.perform_async).toHaveBeenCalledWith({
        vacancy_id: 1,
      });
    });

    it('should not enqueue jobs when no followup action is specified', async () => {
      const data = {
        name: 'Updated Software Engineer',
      };

      await service.update(1, data);

      expect(SetJobDescriptionJob.perform_async).not.toHaveBeenCalled();
      expect(SetVacancyGroupVariablesJob.perform_async).not.toHaveBeenCalled();
    });

    it('should handle job enqueueing errors gracefully', async () => {
      SetJobDescriptionJob.perform_async.mockRejectedValue(new Error('Queue error'));

      const data = {
        name: 'Updated Software Engineer',
        followup_action: 'generate_jobdesc',
      };

      // Should still update the vacancy even if job enqueueing fails
      await expect(service.update(1, data)).rejects.toThrow('Queue error');

      expect(mockVacancy.update).toHaveBeenCalled();
    });

    it('should validate vacancy status before allowing followup actions', async () => {
      mockVacancy.status = 'generating_jobdesc';

      const data = {
        name: 'Updated Software Engineer',
        followup_action: 'generate_jobdesc',
      };

      await expect(service.update(1, data)).rejects.toThrow(
        'Cannot update this vacancy, current status: generating_jobdesc',
      );

      expect(SetJobDescriptionJob.perform_async).not.toHaveBeenCalled();
    });

    it('should allow followup actions for active status', async () => {
      mockVacancy.status = 'active';

      const data = {
        name: 'Updated Software Engineer',
        followup_action: 'generate_jobdesc',
      };

      await service.update(1, data);

      expect(SetJobDescriptionJob.perform_async).toHaveBeenCalledWith({
        vacancy_id: 1,
      });
    });
  });

  describe('job priority and options', () => {
    it('should enqueue jobs with default priority', async () => {
      const data = {
        name: 'Software Engineer',
      };

      await service.create(data);

      expect(SetJobDescriptionJob.perform_async).toHaveBeenCalledWith(
        { vacancy_id: 1 },
        // No options passed, so default priority should be used
      );
    });

    it('should be able to enqueue jobs with custom options in the future', async () => {
      // This test demonstrates how custom options could be added
      const data = {
        name: 'Urgent Software Engineer',
        priority: 'high',
      };

      await service.create(data);

      // Currently, we don't pass priority, but this shows how it could be extended
      expect(SetJobDescriptionJob.perform_async).toHaveBeenCalledWith({
        vacancy_id: 1,
      });

      // In the future, this could be:
      // expect(SetJobDescriptionJob.perform_async).toHaveBeenCalledWith(
      //   { vacancy_id: 1 },
      //   { priority: 1 } // High priority
      // );
    });
  });

  describe('error scenarios', () => {
    it('should handle database errors during vacancy creation', async () => {
      JobVacancy.create.mockRejectedValue(new Error('Database error'));

      const data = {
        name: 'Software Engineer',
      };

      await expect(service.create(data)).rejects.toThrow('Database error');

      // Job should not be enqueued if vacancy creation fails
      expect(SetJobDescriptionJob.perform_async).not.toHaveBeenCalled();
    });

    it('should handle database errors during vacancy update', async () => {
      mockVacancy.update.mockRejectedValue(new Error('Database error'));

      const data = {
        name: 'Updated Software Engineer',
        followup_action: 'generate_jobdesc',
      };

      await expect(service.update(1, data)).rejects.toThrow('Database error');

      // Job should not be enqueued if vacancy update fails
      expect(SetJobDescriptionJob.perform_async).not.toHaveBeenCalled();
    });
  });
});
