const { Queue } = require('bullmq');
const { getRedisConnection } = require('../config/redis');
const config = require('../config/config');

class ApplicationJob {
  constructor() {
    this.queueName = 'default';
    this.redis = getRedisConnection();
    this.queue = new Queue(this.queueName, {
      connection: this.redis,
      defaultJobOptions: config.queue.defaultJobOptions,
    });
  }

  /**
   * Sidekiq-like interface for enqueuing jobs
   * @param {Object} data - Job data to be processed
   * @param {Object} options - Job options (priority, delay, etc.)
   * @returns {Promise<Job>} The enqueued job
   */
  static async perform_async(data = {}, options = {}) {
    const instance = new this();
    const jobName = this.name;

    // Default options with priority support (lower number = higher priority)
    const jobOptions = {
      priority: options.priority || 0,
      delay: options.delay || 0,
      attempts: options.attempts || config.queue.defaultJobOptions.attempts,
      backoff: options.backoff || config.queue.defaultJobOptions.backoff,
      removeOnComplete: options.removeOnComplete || config.queue.defaultJobOptions.removeOnComplete,
      removeOnFail: options.removeOnFail || config.queue.defaultJobOptions.removeOnFail,
      ...options,
    };

    return instance.queue.add(jobName, data, jobOptions);
  }

  /**
   * The main job processing method - must be implemented by subclasses
   * @param {Object} data - Job data
   * @returns {Promise<any>} Job result
   */
  async perform(_data) {
    throw new Error(`${this.constructor.name} must implement the perform method`);
  }

  /**
   * Hook called before job processing
   * @param {Object} data - Job data
   */
  async before_perform(_data) {
    // Override in subclasses if needed
  }

  /**
   * Hook called after successful job processing
   * @param {Object} data - Job data
   * @param {any} result - Job result
   */
  async after_perform(_data, _result) {
    // Override in subclasses if needed
  }

  /**
   * Hook called when job fails
   * @param {Object} data - Job data
   * @param {Error} error - The error that occurred
   */
  async on_failure(data, error) {
    console.error(`Job ${this.constructor.name} failed:`, error);
  }

  /**
   * Get the queue instance
   * @returns {Queue} The Bull queue instance
   */
  getQueue() {
    return this.queue;
  }

  /**
   * Get the Redis connection
   * @returns {Redis} The Redis connection instance
   */
  getRedis() {
    return this.redis;
  }

  /**
   * Close the queue connection
   * Note: Redis connection is shared and managed centrally, so we don't close it here
   */
  async close() {
    await this.queue.close();
    // Redis connection is shared and managed by the centralized redis config
    // It will be closed when the application shuts down
  }
}

module.exports = ApplicationJob;
