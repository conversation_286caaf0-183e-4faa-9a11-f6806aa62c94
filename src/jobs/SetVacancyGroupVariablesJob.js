const ApplicationJob = require('./ApplicationJob');
const { QdrantClient } = require('@qdrant/js-client-rest');
const { JobVacancy, VacancyGroupVariable, Bone, sequelize } = require('../models');
const OnetService = require('../services/OnetService');
const GoogleAiService = require('../services/external/GoogleAiService');
const config = require('../config/config');

class SetVacancyGroupVariablesJob extends ApplicationJob {
  constructor() {
    super();
    this.onetService = new OnetService();
    this.googleAiService = new GoogleAiService();
    this.qdrantClient = new QdrantClient({
      url: config.qdrantUrl,
      apiKey: config.qdrantApiKey,
    });
  }

  /**
   * Process the vacancy group variables generation
   * @param {Object} data - Job data containing vacancy_id
   * @returns {Promise<Object>} Job result
   */
  async perform(data) {
    const { vacancy_id } = data;

    if (!vacancy_id) {
      throw new Error('vacancy_id is required for SetVacancyGroupVariablesJob');
    }

    // Fetch the vacancy
    const vacancy = await JobVacancy.findByPk(vacancy_id);

    if (!vacancy) {
      throw new Error(`Job vacancy with id ${vacancy_id} not found`);
    }

    try {
      console.log(`Generating vacancy group variables for vacancy ${vacancy_id}: ${vacancy.name}`);

      const ksao = await this.generateKsao(vacancy);

      const vgvRecords = await this.ksaoMatching({
        ksao,
        jobVacancyId: vacancy.id,
        boneId: vacancy.bone_id,
      });

      if (vgvRecords.length > 0) {
        await VacancyGroupVariable.bulkCreate(vgvRecords, {
          updateOnDuplicate: [
            'keyword_match_count',
            'keyword_total_count',
            'match_type',
            'weight',
            'bone_value',
          ],
        });
      }

      await vacancy.update({
        ksao,
        status: 'draft',
      });

      console.log(
        `Successfully generated ${vgvRecords.length} vacancy group variables for vacancy ${vacancy_id}`,
      );

      return {
        vacancy_id,
        status: 'completed',
        ksao,
        vgv_records_count: vgvRecords.length,
      };
    } catch (error) {
      if (process.env.NODE_ENV !== 'test') {
        console.error(`Error generating vacancy group variables for vacancy ${vacancy_id}:`, error);
      }

      // Update vacancy status to draft even on error
      await vacancy.update({ status: 'draft' });

      throw error;
    }
  }

  /**
   * Generate KSAO for a vacancy (copied from JobVacancyService)
   * @param {Object} vacancy - Job vacancy object
   * @returns {Promise<Object>} Generated KSAO
   */
  async generateKsao(vacancy) {
    const vacancyName = vacancy.name;
    const jobDetails = vacancy.detailed_descriptions || {};
    const jobDesc = jobDetails.key_responsibilities || vacancy.job_desc;
    const onetsocCodes = vacancy.related_onetsoc_codes;
    const occupations = await this.getOccupationsData(onetsocCodes);

    const [systemPrompt, userPrompt] = await Promise.all([
      this.getSystemPrompt(),
      this.getUserPrompt(vacancyName, jobDesc, occupations, jobDetails),
    ]);

    const aiParams = {
      model: 'gemini-2.5-flash',
      contents: [{ role: 'user', parts: [{ text: userPrompt }] }],
      config: {
        temperature: 0.2,
        responseMimeType: 'application/json',
        thinkingConfig: { thinkingBudget: -1 },
        systemInstruction: [{ text: systemPrompt }],
      },
    };

    const response = await this.googleAiService.generateContent(aiParams);
    return JSON.parse(response.candidates[0].content.parts[0].text);
  }

  /**
   * Get occupations data from ONET codes
   * @param {Array} onetsocCodes - Array of ONET SOC codes
   * @returns {Promise<Object>} Occupations data
   */
  async getOccupationsData(onetsocCodes) {
    if (!onetsocCodes || onetsocCodes.length === 0) {
      return {};
    }

    const onetResults = await Promise.all([
      this.onetService.getOccupations(onetsocCodes),
      this.onetService.getKnowledges(onetsocCodes),
      this.onetService.getSkills(onetsocCodes),
      this.onetService.getAbilities(onetsocCodes),
      this.onetService.getInterests(onetsocCodes),
      this.onetService.getWorkValues(onetsocCodes),
      this.onetService.getWorkStyles(onetsocCodes),
    ]);

    const occupations = onetResults[0];
    const referenceData = {
      knowledges: onetResults[1],
      skills: onetResults[2],
      abilities: onetResults[3],
      interests: onetResults[4],
      work_values: onetResults[5],
      work_styles: onetResults[6],
    };

    // Merge reference data into occupations
    Object.keys(occupations).forEach(onetsocCode => {
      occupations[onetsocCode] = {
        ...occupations[onetsocCode],
        knowledges: referenceData.knowledges.filter(item => item.onetsoc_code === onetsocCode),
        skills: referenceData.skills.filter(item => item.onetsoc_code === onetsocCode),
        abilities: referenceData.abilities.filter(item => item.onetsoc_code === onetsocCode),
        interests: referenceData.interests.filter(item => item.onetsoc_code === onetsocCode),
        work_values: referenceData.work_values.filter(item => item.onetsoc_code === onetsocCode),
        work_styles: referenceData.work_styles.filter(item => item.onetsoc_code === onetsocCode),
      };
    });

    return occupations;
  }

  /**
   * Get system prompt for KSAO generation
   * @returns {Promise<string>} System prompt
   */
  async getSystemPrompt() {
    return Promise.resolve(`
      ## Role and Context
      You are an expert HR professional specializing in job analysis and competency modeling. Your expertise includes translating job requirements into structured Knowledge, Skills, Abilities, and Other characteristics (KSAO) frameworks that align with industry standards and O*NET occupational data.

      ## Task Overview
      Create a comprehensive KSAO profile for a specific job vacancy by analyzing job descriptions and leveraging O*NET occupational data as supporting reference material.

      ## Output Requirements
      Generate a JSON object with the following structure:
      {
        "knowledge": ["item1", "item2", ...],
        "skills": ["item1", "item2", ...],
        "abilities": ["item1", "item2", ...],
        "other_characteristics": ["item1", "item2", ...]
      }

      ## Guidelines
      1. **Knowledge**: Include domain-specific knowledge areas, technical expertise, and theoretical understanding required
      2. **Skills**: Focus on practical, learnable competencies and technical skills
      3. **Abilities**: Include cognitive, physical, and sensory capabilities
      4. **Other Characteristics**: Include personality traits, work styles, and preferences

      Each category should contain 5-10 relevant items that are:
      - Specific and actionable
      - Directly relevant to the job
      - Measurable or observable
      - Industry-appropriate
    `);
  }

  /**
   * Get user prompt for KSAO generation
   * @param {string} vacancyName - Vacancy name
   * @param {Array} jobDesc - Job description
   * @param {Object} occupations - Occupations data
   * @param {Object} jobDetails - Job details
   * @returns {Promise<string>} User prompt
   */
  async getUserPrompt(vacancyName, jobDesc, occupations, jobDetails) {
    let userPrompt = `# Job Analysis Request\n\n`;
    userPrompt += `## Job Title\n${vacancyName}\n\n`;
    userPrompt += `## Job Description\n`;

    if (Array.isArray(jobDesc)) {
      jobDesc.forEach((desc, index) => {
        userPrompt += `${index + 1}. ${desc}\n`;
      });
    } else {
      userPrompt += `${jobDesc}\n`;
    }

    userPrompt += `\n## Additional Job Details\n`;
    if (jobDetails && Object.keys(jobDetails).length > 0) {
      Object.entries(jobDetails).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          userPrompt += `### ${key.replace('_', ' ')}\n`;
          value.forEach((item, index) => {
            userPrompt += `${index + 1}. ${item}\n`;
          });
        } else if (typeof value === 'object') {
          userPrompt += `### ${key.replace('_', ' ')}\n`;
          userPrompt += `\`\`\`json\n${JSON.stringify(value, null, 2)}\n\`\`\`\n`;
        } else {
          userPrompt += `### ${key.replace('_', ' ')}\n${value}\n`;
        }
        userPrompt += `\n`;
      });
    }

    userPrompt += '\n# Related O*NET Data\n';

    await Promise.all(
      Object.values(occupations).map(async occupation => {
        userPrompt += `## Occupation: ${occupation.title}\n`;
        userPrompt += `### Description\n${occupation.description}\n\n`;

        await Promise.all(
          ['knowledges', 'skills', 'abilities', 'interests', 'work_values', 'work_styles'].map(
            async key => {
              if (occupation[key] && occupation[key].length > 0) {
                userPrompt += `### ${key.replace('_', ' ')}\n`;
                userPrompt += `\`\`\`json\n`;
                userPrompt += JSON.stringify(occupation[key], null, 2);
                userPrompt += `\n\`\`\`\n\n`;
              }
            },
          ),
        );
      }),
    );

    return Promise.resolve(userPrompt);
  }

  /**
   * Perform KSAO matching (copied from JobVacancyService)
   * @param {Object} params - Parameters for KSAO matching
   * @returns {Promise<Array>} VGV records
   */
  async ksaoMatching({ ksao, jobVacancyId, boneId }) {
    const vgvRecords = [];

    const [jgvs, flattenedKsao] = await Promise.all([
      this.getJobGroupVariables(),
      this.flattenKsao(ksao),
    ]);

    let boneName;
    if (boneId) {
      boneName = (await Bone.findByPk(boneId)).name;
    }

    // TODO: move to db later
    const boneMappings = {
      1: {
        Business: 7,
        Functional: 5,
        Professional: 6,
      },
      2: {
        Business: 6,
        Functional: 4,
        Professional: 3,
      },
      3: {
        Business: 5,
        Functional: 4,
        Professional: 7,
      },
      4: {
        Business: 6,
        Functional: 7,
        Professional: 5,
      },
      5: {
        Business: 7,
        Functional: 5,
        Professional: 5,
      },
      6: {
        Business: 7,
        Functional: 5,
        Professional: 6,
      },
      7: {
        Business: 7,
        Functional: 5,
        Professional: 4,
      },
      8: {
        Business: 6,
        Functional: 5,
        Professional: 5,
      },
    };

    await Promise.all(
      jgvs.map(async jgv => {
        const matchCount = jgv.keywords.reduce((acc, keyword) => {
          return acc + (flattenedKsao.includes(keyword) ? 1 : 0);
        }, 0);

        const boneMapping = boneMappings[jgv.id];
        const boneValue = boneMapping[boneName] || 0;

        vgvRecords.push({
          job_vacancy_id: jobVacancyId,
          job_group_variable_id: jgv.id,
          keyword_match_count: matchCount,
          keyword_total_count: jgv.keywords.length,
          match_type: 'weight',
          weight: 1 / jgvs.length,
          bone_value: boneValue,
        });
      }),
    );

    return vgvRecords;
  }

  /**
   * Get job group variables
   * @returns {Promise<Array>} Job group variables
   */
  async getJobGroupVariables() {
    return sequelize.query('SELECT id, keywords FROM job_group_variables', {
      type: sequelize.QueryTypes.SELECT,
    });
  }

  /**
   * Flatten KSAO object
   * @param {Object} ksao - KSAO object
   * @returns {Promise<string>} Flattened KSAO string
   */
  async flattenKsao(ksao) {
    return Object.values(ksao)
      .flat()
      .map(item => item.toLowerCase())
      .join(';');
  }

  /**
   * Hook called before job processing
   * @param {Object} data - Job data
   */
  async before_perform(data) {
    console.log(`Starting SetVacancyGroupVariablesJob for vacancy ${data.vacancy_id}`);
  }

  /**
   * Hook called after successful job processing
   * @param {Object} data - Job data
   * @param {any} result - Job result
   */
  async after_perform(data, _result) {
    console.log(`Completed SetVacancyGroupVariablesJob for vacancy ${data.vacancy_id}`);
  }

  /**
   * Hook called when job fails
   * @param {Object} data - Job data
   * @param {Error} error - The error that occurred
   */
  async on_failure(data, error) {
    console.error(`SetVacancyGroupVariablesJob failed for vacancy ${data.vacancy_id}:`, error);
  }
}

module.exports = SetVacancyGroupVariablesJob;
