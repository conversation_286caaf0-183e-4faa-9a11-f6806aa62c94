'use strict';

const fs = require('fs');
const path = require('path');

const jobs = {};
const basename = path.basename(__filename);

// Automatically load all job classes from the jobs directory
fs.readdirSync(__dirname)
  .filter(file => {
    return (
      file.indexOf('.') !== 0 &&
      file !== basename &&
      file !== 'ApplicationJob.js' && // Exclude base class
      file.slice(-3) === '.js'
    );
  })
  .forEach(file => {
    const JobClass = require(path.join(__dirname, file));
    const jobName = path.basename(file, '.js');
    jobs[jobName] = JobClass;
  });

module.exports = jobs;
