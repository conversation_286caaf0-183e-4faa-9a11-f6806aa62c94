const Redis = require('ioredis');
const config = require('./config');

/**
 * Centralized Redis connection management
 * Provides a singleton Redis instance that can be reused across the application
 * to avoid creating multiple connections to the same Redis server.
 */
class RedisConnection {
  constructor() {
    this.connection = null;
  }

  /**
   * Get or create the Redis connection instance
   * @returns {Redis} The Redis connection instance
   */
  getInstance() {
    if (!this.connection) {
      console.log('Creating new Redis connection...');
      this.connection = new Redis(config.redis);

      // Add connection event handlers
      this.connection.on('connect', () => {
        console.log('✅ Redis connected successfully');
      });

      this.connection.on('error', error => {
        console.error('❌ Redis connection error:', error);
      });

      this.connection.on('close', () => {
        console.log('🔌 Redis connection closed');
      });

      this.connection.on('reconnecting', () => {
        console.log('🔄 Redis reconnecting...');
      });
    }

    return this.connection;
  }

  /**
   * Close the Redis connection
   */
  async close() {
    if (this.connection) {
      console.log('🔌 Closing Redis connection...');
      await this.connection.quit();
      this.connection = null;
      console.log('✅ Redis connection closed');
    }
  }

  /**
   * Check if Redis connection is active
   * @returns {boolean} True if connection exists and is not closing
   */
  isConnected() {
    return this.connection && this.connection.status === 'ready';
  }
}

// Create singleton instance
const redisConnection = new RedisConnection();

module.exports = {
  /**
   * Get the Redis connection instance
   * @returns {Redis} The Redis connection instance
   */
  getRedisConnection: () => redisConnection.getInstance(),

  /**
   * Close the Redis connection
   */
  closeRedisConnection: () => redisConnection.close(),

  /**
   * Check if Redis connection is active
   * @returns {boolean} True if connection is active
   */
  isRedisConnected: () => redisConnection.isConnected(),

  // Export the connection manager for testing
  _redisConnection: redisConnection,
};
