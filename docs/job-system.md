# Job System Documentation

## Overview

The Paragon API uses BullMQ for background job processing, providing a
Sidekiq-like interface for enqueueing and processing jobs. This system handles
long-running tasks asynchronously, improving API response times and user
experience.

## Architecture

### Components

1. **ApplicationJob** - Base class for all jobs with Sidekiq-like interface
2. **Job Classes** - Specific job implementations (e.g., SetJobDescriptionJob)
3. **Worker Process** - Separate process that processes jobs from the queue
4. **Redis** - Message broker and job storage
5. **Bull Board** - Web dashboard for monitoring jobs

### Queue Structure

- **Single Queue**: `default` - All jobs are processed through this queue
- **Priority Support**: Jobs can be assigned priorities (lower number = higher
  priority)
- **Concurrency**: Configurable concurrent job processing (default: 10)

## Configuration

### Environment Variables

```bash
# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_MAX_RETRIES=3
REDIS_RETRY_DELAY=100

# Queue Configuration
QUEUE_REMOVE_ON_COMPLETE=100
QUEUE_REMOVE_ON_FAIL=50
QUEUE_DEFAULT_ATTEMPTS=3
QUEUE_BACKOFF_DELAY=2000
QUEUE_CONCURRENCY=10
```

### Configuration File

The job system configuration is defined in `src/config/config.js`:

```javascript
redis: {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT, 10) || 6379,
  password: process.env.REDIS_PASSWORD || undefined,
  db: parseInt(process.env.REDIS_DB, 10) || 0,
  // ... other Redis options
},

queue: {
  defaultJobOptions: {
    removeOnComplete: parseInt(process.env.QUEUE_REMOVE_ON_COMPLETE, 10) || 100,
    removeOnFail: parseInt(process.env.QUEUE_REMOVE_ON_FAIL, 10) || 50,
    attempts: parseInt(process.env.QUEUE_DEFAULT_ATTEMPTS, 10) || 3,
    // ... other job options
  },
  concurrency: parseInt(process.env.QUEUE_CONCURRENCY, 10) || 10,
}
```

## Creating Jobs

### 1. Create a Job Class

Create a new job class in `src/jobs/` that extends `ApplicationJob`:

```javascript
const ApplicationJob = require('./ApplicationJob');

class MyCustomJob extends ApplicationJob {
  /**
   * Main job processing method
   * @param {Object} data - Job data
   * @returns {Promise<any>} Job result
   */
  async perform(data) {
    const { param1, param2 } = data;

    // Your job logic here
    console.log(`Processing job with params: ${param1}, ${param2}`);

    // Return result
    return { status: 'completed', processedAt: new Date() };
  }

  /**
   * Optional: Hook called before job processing
   */
  async before_perform(data) {
    console.log(`Starting MyCustomJob with data:`, data);
  }

  /**
   * Optional: Hook called after successful job processing
   */
  async after_perform(data, result) {
    console.log(`Completed MyCustomJob:`, result);
  }

  /**
   * Optional: Hook called when job fails
   */
  async on_failure(data, error) {
    console.error(`MyCustomJob failed:`, error);
  }
}

module.exports = MyCustomJob;
```

### 2. Register the Job

Add your job class to the worker in `src/worker.js`:

```javascript
// Import your job class
const MyCustomJob = require('./jobs/MyCustomJob');

// Add to jobClasses map
this.jobClasses = {
  SetJobDescriptionJob,
  SetVacancyGroupVariablesJob,
  MyCustomJob, // Add your job here
};
```

## Enqueueing Jobs

### Basic Usage

```javascript
const MyCustomJob = require('../jobs/MyCustomJob');

// Enqueue a job
await MyCustomJob.perform_async({
  param1: 'value1',
  param2: 'value2',
});
```

### With Options

```javascript
await MyCustomJob.perform_async(
  { param1: 'value1', param2: 'value2' },
  {
    priority: 1, // Higher priority (lower number = higher priority)
    delay: 5000, // Delay execution by 5 seconds
    attempts: 5, // Override default retry attempts
    removeOnComplete: 10, // Keep only 10 completed jobs
  },
);
```

## Running the Worker

### Development

```bash
# Start worker in development mode (with auto-restart)
npm run worker:dev

# Start worker in production mode
npm run worker
```

### Production Deployment

For production, you should run the worker as a separate service:

```bash
# Using PM2
pm2 start src/worker.js --name "paragon-worker"

# Using systemd (create a service file)
sudo systemctl start paragon-worker
```

## Monitoring

### Bull Board Dashboard

The Bull Board dashboard is available at `/admin/queues` (only in non-production
environments):

- View active, completed, and failed jobs
- Retry failed jobs
- Monitor queue statistics
- Real-time job progress

### Logs

The worker provides detailed logging:

```
🚀 Worker started with concurrency: 10
📋 Registered job types: SetJobDescriptionJob, SetVacancyGroupVariablesJob
⏳ Waiting for jobs...
Processing job: SetJobDescriptionJob with data: { vacancy_id: 123 }
✅ Job SetJobDescriptionJob (456) completed successfully
```

## Error Handling

### Automatic Retries

Jobs automatically retry on failure based on configuration:

- Default attempts: 3
- Backoff strategy: Exponential (2s, 4s, 8s, ...)
- Failed jobs are kept for debugging

### Custom Error Handling

Implement the `on_failure` hook in your job class:

```javascript
async on_failure(data, error) {
  // Log error
  console.error(`Job failed:`, error);

  // Send notification
  await this.notifyAdmins(error);

  // Update database status
  await this.updateJobStatus(data.id, 'failed');
}
```

## Best Practices

### 1. Job Design

- Keep jobs idempotent (safe to run multiple times)
- Use specific job classes for different tasks
- Pass minimal data (IDs rather than full objects)
- Handle edge cases and validation

### 2. Error Handling

- Always implement proper error handling
- Use the `on_failure` hook for cleanup
- Log errors with sufficient context
- Consider dead letter queues for persistent failures

### 3. Performance

- Use appropriate concurrency settings
- Monitor Redis memory usage
- Clean up completed/failed jobs regularly
- Use job priorities wisely

### 4. Testing

- Mock external services in tests
- Test job logic separately from queue mechanics
- Use test-specific Redis database

## Existing Jobs

### SetJobDescriptionJob

Generates job descriptions using AI services.

**Data**: `{ vacancy_id: number }`

**Process**:

1. Fetches job vacancy from database
2. Calls GenerateJobDescService
3. Updates vacancy with generated description

### SetVacancyGroupVariablesJob

Generates KSAO profiles and matches them with job group variables.

**Data**: `{ vacancy_id: number }`

**Process**:

1. Fetches job vacancy from database
2. Generates KSAO profile using AI
3. Matches KSAO with job group variables
4. Updates vacancy with results

## Troubleshooting

### Common Issues

1. **Redis Connection Errors**
   - Check Redis server is running
   - Verify connection settings
   - Check network connectivity

2. **Jobs Not Processing**
   - Ensure worker is running
   - Check worker logs for errors
   - Verify job is properly registered

3. **Memory Issues**
   - Monitor Redis memory usage
   - Adjust job retention settings
   - Consider Redis persistence settings

### Debugging

1. **Enable Debug Logging**

   ```bash
   DEBUG=bull* npm run worker
   ```

2. **Check Bull Board Dashboard**
   - View failed jobs
   - Check error messages
   - Monitor queue statistics

3. **Redis CLI**
   ```bash
   redis-cli
   > KEYS bull:default:*
   > LLEN bull:default:waiting
   ```
